/**
 * Utility để tạo mã nhân viên tự động
 */

/**
 * Tạo mã nhân viên với tiền tố REDAI + số ngẫu nhiên
 * @returns Mã nhân viên duy nhất với format REDAI + 6 chữ số
 * @example "REDAI123456"
 */
export const generateEmployeeCode = (): string => {
  // Tạo số ngẫu nhiên 6 chữ số (100000 - 999999)
  const randomNumber = Math.floor(Math.random() * 900000) + 100000;
  
  // Kết hợp tiền tố REDAI với số ngẫu nhiên
  return `REDAI${randomNumber}`;
};

/**
 * Tạo mã nhân viên với timestamp để đảm bảo tính duy nhất cao hơn
 * @returns Mã nhân viên với format REDAI + timestamp cuối + số ngẫu nhiên
 * @example "REDAI789123456" (789 là 3 số cuối của timestamp)
 */
export const generateEmployeeCodeWithTimestamp = (): string => {
  // Lấy 3 số cuối của timestamp hiện tại
  const timestamp = Date.now();
  const timestampSuffix = timestamp.toString().slice(-3);
  
  // Tạo số ngẫu nhiên 3 chữ số (100 - 999)
  const randomNumber = Math.floor(Math.random() * 900) + 100;
  
  // Kết hợp tiền tố REDAI với timestamp và số ngẫu nhiên
  return `REDAI${timestampSuffix}${randomNumber}`;
};
