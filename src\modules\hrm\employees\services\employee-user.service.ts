import { Injectable, Logger } from '@nestjs/common';
import { EmployeeRepository } from '../repositories/employee.repository';
import { CreateEmployeeWithUserDto } from '../dto/create-employee-with-user.dto';
import {
  EmployeeWithUserResponseDto,
  EmployeeResponseDto,
  UserResponseDto,
} from '../dto/employee-with-user-response.dto';
import { AppException } from '@/common/exceptions/app.exception';
import { HRM_ERROR_CODES } from '../../errors/hrm-error.code';
import { EmployeeStatus } from '../enum/employee-status.enum';
import { UserStatus } from '@/modules/auth/enum/user-status.enum';
import { Transactional } from 'typeorm-transactional';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@/modules/auth/entities/user.entity';
import { EncryptionService } from '@/shared/services/encryption.service';
import { CreateUserForEmployeeDto } from '../dto/create-user-for-employee.dto';
import { LinkEmployeeToUserDto } from '../dto/link-employee-to-user.dto';
import { generateEmployeeCode } from '@/shared/utils/generators/employee-code-generator.util';
import { UserRepository } from '@/modules/auth/repositories/user.repository';

/**
 * Service cho việc tạo nhân viên kèm tài khoản người dùng
 */
@Injectable()
export class EmployeeUserService {
  private readonly logger = new Logger(EmployeeUserService.name);

  constructor(
    private readonly employeeRepository: EmployeeRepository,
    private readonly userRepository: UserRepository,
    @InjectRepository(User)
    private readonly userEntityRepository: Repository<User>,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Tạo nhân viên mới kèm tài khoản người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param createDto Dữ liệu tạo nhân viên và tài khoản
   * @param currentUserId ID của người dùng đang thực hiện thao tác
   * @returns Thông tin nhân viên và tài khoản đã tạo
   */
  @Transactional()
  async createEmployeeWithUser(
    tenantId: number,
    createDto: CreateEmployeeWithUserDto,
    currentUserId: number,
  ): Promise<EmployeeWithUserResponseDto> {
    try {
      // Tự động tạo mã nhân viên với tiền tố REDAI + số ngẫu nhiên
      let employeeCode: string;
      let attempts = 0;
      const maxAttempts = 10;

      // Thử tạo mã nhân viên duy nhất (tối đa 10 lần thử)
      do {
        employeeCode = generateEmployeeCode();
        const existingEmployee = await this.employeeRepository.findByEmployeeCode(
          tenantId,
          employeeCode,
        );

        if (!existingEmployee) {
          break; // Mã nhân viên duy nhất, thoát khỏi vòng lặp
        }

        attempts++;
        if (attempts >= maxAttempts) {
          throw new AppException(
            HRM_ERROR_CODES.EMPLOYEE_CREATE_FAILED,
            'Không thể tạo mã nhân viên duy nhất sau nhiều lần thử',
          );
        }
      } while (attempts < maxAttempts);

      // Kiểm tra email đã tồn tại chưa
      const existingEmail = await this.userRepository.findByEmail(
        createDto.userInfo.email,
      );
      if (existingEmail) {
        throw new AppException(
          HRM_ERROR_CODES.EMAIL_EXISTS,
          `Email ${createDto.userInfo.email} đã được sử dụng`,
        );
      }

      // Mã hóa mật khẩu
      const hashedPassword = this.encryptionService.hashPassword(
        createDto.userInfo.password,
      );

      // Tạo timestamp hiện tại
      const now = Date.now();

      // Tạo tài khoản người dùng mới
      const user = await this.userRepository.create({
        email: createDto.userInfo.email,
        password: hashedPassword,
        fullName: createDto.userInfo.fullName,
        departmentId: createDto.departmentId,
        status: UserStatus.ACTIVE,
        createdAt: now,
      });

      // Tạo nhân viên mới với mã đã generate
      const employee = await this.employeeRepository.create(tenantId, {
        employeeCode, // Sử dụng mã nhân viên đã tự động tạo
        employeeName: createDto.employeeName,
        departmentId: createDto.departmentId,
        jobTitle: createDto.jobTitle,
        status: createDto.status || EmployeeStatus.ACTIVE,
        createdAt: now,
        updatedAt: now,
        createdBy: currentUserId,
        updatedBy: currentUserId,
      });

      // Tạo đối tượng phản hồi
      const employeeResponse = new EmployeeResponseDto();
      employeeResponse.id = employee.id;
      employeeResponse.employeeCode = employee.employeeCode;
      employeeResponse.employeeName = employee.employeeName;
      employeeResponse.departmentId = employee.departmentId;
      employeeResponse.jobTitle = employee.jobTitle;
      employeeResponse.status = employee.status;

      const userResponse = new UserResponseDto();
      userResponse.id = user.id;
      userResponse.email = user.email;
      userResponse.fullName = user.fullName;

      const response: EmployeeWithUserResponseDto = {
        employee: employeeResponse,
        user: userResponse,
      };

      this.logger.log(`Đã tạo nhân viên và tài khoản thành công với mã nhân viên: ${employeeCode}`);
      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi tạo nhân viên và tài khoản: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_CREATE_FAILED,
        `Không thể tạo nhân viên và tài khoản: ${error.message}`,
      );
    }
  }

  /**
   * Tạo tài khoản người dùng cho nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param createDto Dữ liệu tạo tài khoản người dùng
   * @param currentUserId ID của người dùng đang thực hiện thao tác
   * @returns Thông tin người dùng đã tạo
   */
  @Transactional()
  async createUserForEmployee(
    tenantId: number,
    createDto: CreateUserForEmployeeDto,
    currentUserId: number,
  ): Promise<UserResponseDto> {
    try {

      // Kiểm tra email đã tồn tại chưa
      const existingEmail = await this.userRepository.findByEmail(
        createDto.email,
      );
      if (existingEmail) {
        throw new AppException(
          HRM_ERROR_CODES.EMAIL_EXISTS,
          `Email ${createDto.email} đã được sử dụng`,
        );
      }

      // Nếu có employeeId, kiểm tra nhân viên có tồn tại không
      if (createDto.employeeId) {
        const employee = await this.employeeRepository.findById(
          tenantId,
          createDto.employeeId,
        );
        if (!employee) {
          throw new AppException(
            HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
            `Không tìm thấy nhân viên với ID ${createDto.employeeId}`,
          );
        }
      }

      // Mã hóa mật khẩu
      const hashedPassword = this.encryptionService.hashPassword(
        createDto.password,
      );

      // Tạo timestamp hiện tại
      const now = Date.now();

      // Tạo tài khoản người dùng mới
      const user = await this.userRepository.create({
        email: createDto.email,
        password: hashedPassword,
        fullName: createDto.fullName,
        departmentId: createDto.departmentId,
        employeeId: createDto.employeeId,
        status: UserStatus.ACTIVE,
        createdAt: now,
        userType: 'EMPLOYEE',
      });

      // Nếu có employeeId, cập nhật thông tin nhân viên
      if (createDto.employeeId) {
        await this.employeeRepository.update(tenantId, createDto.employeeId, {
          updatedAt: now,
          updatedBy: currentUserId,
        });
      }

      // Tạo đối tượng phản hồi
      const userResponse = new UserResponseDto();
      userResponse.id = user.id;
      userResponse.email = user.email;
      userResponse.fullName = user.fullName;

      return userResponse;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi tạo tài khoản người dùng: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.USER_CREATION_FAILED,
        `Không thể tạo tài khoản người dùng: ${error.message}`,
      );
    }
  }

  /**
   * Gắn nhân viên với tài khoản người dùng
   * @param tenantId ID tenant (required for tenant isolation)
   * @param linkDto Dữ liệu gắn nhân viên với tài khoản
   * @param currentUserId ID của người dùng đang thực hiện thao tác
   * @returns Thông tin nhân viên và tài khoản đã gắn
   */
  @Transactional()
  async linkEmployeeToUser(
    tenantId: number,
    linkDto: LinkEmployeeToUserDto,
    currentUserId: number,
  ): Promise<EmployeeWithUserResponseDto> {
    try {
      // Kiểm tra nhân viên có tồn tại không
      const employee = await this.employeeRepository.findById(
        tenantId,
        linkDto.employeeId,
      );
      if (!employee) {
        throw new AppException(
          HRM_ERROR_CODES.EMPLOYEE_NOT_FOUND,
          `Không tìm thấy nhân viên với ID ${linkDto.employeeId}`,
        );
      }

      // Kiểm tra người dùng có tồn tại không
      const user = await this.userRepository.findById(linkDto.userId);
      if (!user) {
        throw new AppException(
          HRM_ERROR_CODES.USER_NOT_FOUND,
          `Không tìm thấy người dùng với ID ${linkDto.userId}`,
        );
      }

      // Kiểm tra người dùng đã gắn với nhân viên khác chưa
      if (user.employeeId) {
        throw new AppException(
          HRM_ERROR_CODES.USER_ALREADY_HAS_EMPLOYEE,
          `Người dùng với ID ${linkDto.userId} đã gắn với nhân viên khác`,
        );
      }

      // Tạo timestamp hiện tại
      const now = Date.now();

      // Cập nhật thông tin nhân viên
      await this.employeeRepository.update(tenantId, linkDto.employeeId, {
        updatedAt: now,
        updatedBy: currentUserId,
      });

      // Cập nhật employeeId cho người dùng
      await this.userRepository.update(linkDto.userId, {
        employeeId: linkDto.employeeId,
      });

      // Tạo đối tượng phản hồi
      const employeeResponse = new EmployeeResponseDto();
      employeeResponse.id = employee.id;
      employeeResponse.employeeCode = employee.employeeCode;
      employeeResponse.employeeName = employee.employeeName;
      employeeResponse.departmentId = employee.departmentId;
      employeeResponse.jobTitle = employee.jobTitle;
      employeeResponse.status = employee.status;

      const userResponse = new UserResponseDto();
      userResponse.id = user.id;
      userResponse.email = user.email;
      userResponse.fullName = user.fullName;

      const response: EmployeeWithUserResponseDto = {
        employee: employeeResponse,
        user: userResponse,
      };

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi gắn nhân viên với tài khoản: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        HRM_ERROR_CODES.EMPLOYEE_UPDATE_FAILED,
        `Không thể gắn nhân viên với tài khoản: ${error.message}`,
      );
    }
  }
}
